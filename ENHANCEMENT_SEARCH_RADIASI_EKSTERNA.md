# Enhancement: Fitur Pencarian Tabel Detail Radiasi Eksterna

## Deskripsi
Menambahkan fitur pencarian yang mendukung pencarian berdasarkan kolom "Energi" dan "RTT" (Radioterapis) pada tabel detail radiasi eksterna di file `application/views/Pengkajian/radioTerapi/laporan_tindakan_radiasi_eksterna.php`.

## Perubahan yang Dilakukan

### 1. Model: LaporanRadiasiEksternaModel.php

#### A. Perubahan Array Column Search
**Sebelum:**
```php
var $column_search_detail = array('tanggal', 'energi', 'ssd_sad', 'dosis_fraksi', 'oleh');
```

**Sesudah:**
```php
var $column_search_detail = array('tanggal', 'energi_display', 'ssd_sad', 'dosis_fraksi', 'nama_rtt');
```

#### <PERSON>. <PERSON> Query SELECT
**Sebelum:**
```php
$this->db->select([
    'medis.radiasi_eksterna_detail.*',
    'COALESCE(db_master.variabel.variabel, medis.radiasi_eksterna_detail.energi) as energi_display'
]);
$this->db->from($this->table_detail);
$this->db->join('db_master.variabel', 'db_master.variabel.id_variabel = medis.radiasi_eksterna_detail.energi AND db_master.variabel.id_referensi = 1862', 'left');
```

**Sesudah:**
```php
$this->db->select([
    'medis.radiasi_eksterna_detail.*',
    'COALESCE(db_master.variabel.variabel, medis.radiasi_eksterna_detail.energi) as energi_display',
    'master.getNamaLengkapPegawai(ap.NIP) as nama_rtt'
]);
$this->db->from($this->table_detail);
$this->db->join('db_master.variabel', 'db_master.variabel.id_variabel = medis.radiasi_eksterna_detail.energi AND db_master.variabel.id_referensi = 1862', 'left');
$this->db->join('aplikasi.pengguna ap', 'ap.ID = medis.radiasi_eksterna_detail.oleh', 'left');
```

#### C. Perubahan Logic Pencarian
**Sebelum:**
```php
foreach ($this->column_search_detail as $item) {
    if ($_GET['search']['value']) {
        if ($i === 0) {
            $this->db->group_start();
            $this->db->like($item, $_GET['search']['value']);
        } else {
            $this->db->or_like($item, $_GET['search']['value']);
        }
        if (count($this->column_search_detail) - 1 == $i)
            $this->db->group_end();
    }
    $i++;
}
```

**Sesudah:**
```php
foreach ($this->column_search_detail as $item) {
    if ($_GET['search']['value']) {
        if ($i === 0) {
            $this->db->group_start();
            // Untuk kolom energi_display dan nama_rtt, gunakan LOWER untuk case-insensitive search
            if ($item == 'energi_display' || $item == 'nama_rtt') {
                $this->db->where("LOWER($item) LIKE", '%' . strtolower($_GET['search']['value']) . '%');
            } else {
                $this->db->like($item, $_GET['search']['value']);
            }
        } else {
            // Untuk kolom energi_display dan nama_rtt, gunakan LOWER untuk case-insensitive search
            if ($item == 'energi_display' || $item == 'nama_rtt') {
                $this->db->or_where("LOWER($item) LIKE", '%' . strtolower($_GET['search']['value']) . '%');
            } else {
                $this->db->or_like($item, $_GET['search']['value']);
            }
        }
        if (count($this->column_search_detail) - 1 == $i)
            $this->db->group_end();
    }
    $i++;
}
```

## Fitur yang Ditambahkan

### 1. Pencarian Berdasarkan Energi
- Pencarian dapat dilakukan berdasarkan nama energi (bukan ID)
- Menggunakan data dari tabel `db_master.variabel` yang sudah di-JOIN
- Mendukung partial matching dan case-insensitive search

### 2. Pencarian Berdasarkan RTT (Radioterapis)
- Pencarian dapat dilakukan berdasarkan nama lengkap RTT
- Menggunakan fungsi `master.getNamaLengkapPegawai()` untuk mendapatkan nama lengkap
- Mendukung partial matching dan case-insensitive search

### 3. Pencarian Gabungan
- Pencarian dapat dilakukan secara bersamaan di semua kolom yang didukung
- Menggunakan operator OR untuk mencari di multiple kolom
- Case-insensitive untuk kolom Energi dan RTT

## Kolom yang Mendukung Pencarian

1. **Tanggal** - Pencarian berdasarkan tanggal tindakan
2. **Energi** - Pencarian berdasarkan nama energi (case-insensitive)
3. **SSD/SAD** - Pencarian berdasarkan nilai SSD/SAD
4. **Dosis/Fraksi** - Pencarian berdasarkan nilai dosis per fraksi
5. **RTT** - Pencarian berdasarkan nama RTT/Radioterapis (case-insensitive)

## Cara Penggunaan

1. Buka halaman Laporan Tindakan Radiasi Eksterna
2. Klik tombol "Detail" pada salah satu data radiasi
3. Di modal yang terbuka, pada tabel "Data Detail Radiasi", gunakan kotak pencarian di bagian atas tabel
4. Ketik kata kunci yang ingin dicari (misalnya: nama energi atau nama RTT)
5. Sistem akan menampilkan hasil pencarian secara real-time

## Contoh Pencarian

- **Pencarian Energi**: Ketik "10 MV" untuk mencari semua data dengan energi 10 MV
- **Pencarian RTT**: Ketik "RADIOGRAFER" untuk mencari semua data yang dilakukan oleh RTT dengan nama mengandung "RADIOGRAFER"
- **Pencarian Gabungan**: Ketik "UJICOBA" untuk mencari di semua kolom yang mengandung kata "UJICOBA"

## Kompatibilitas

- Tidak mengubah tampilan UI yang sudah ada
- Menggunakan fitur search bawaan DataTables
- Backward compatible dengan pencarian yang sudah ada sebelumnya
- Tidak mempengaruhi performa secara signifikan karena menggunakan server-side processing

## Testing

Untuk menguji fitur ini:
1. Pastikan ada data detail radiasi dengan berbagai energi dan RTT
2. Coba pencarian dengan kata kunci yang ada di kolom Energi
3. Coba pencarian dengan nama RTT
4. Coba pencarian dengan kata kunci yang ada di kolom lain
5. Verifikasi bahwa pencarian bersifat case-insensitive dan mendukung partial matching
